import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';
import '../models/models.dart';
import 'auth_service.dart';

class HttpService {
  static final HttpService _instance = HttpService._internal();
  factory HttpService() => _instance;
  HttpService._internal();

  late Dio _dio;
  final AuthService _authService = AuthService();

  void initialize() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: Duration(milliseconds: AppConstants.connectionTimeout),
      receiveTimeout: Duration(milliseconds: AppConstants.receiveTimeout),
      sendTimeout: Duration(milliseconds: AppConstants.sendTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(_AuthInterceptor(_authService));
    _dio.interceptors.add(_ErrorInterceptor());
    
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
        error: true,
        logPrint: (obj) => debugPrint(obj.toString()),
      ));
    }
  }

  // GET request
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // POST request
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // PUT request
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // DELETE request
  Future<ApiResponse<T>> delete<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(path, queryParameters: queryParameters);
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // Handle response
  ApiResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? fromJson,
  ) {
    final data = response.data;
    
    if (data is Map<String, dynamic>) {
      final success = data['success'] ?? false;
      final message = data['message'] ?? '';
      final messageAr = data['message_ar'];
      final responseData = data['data'];
      final meta = data['meta'];
      final errors = data['errors'];
      final code = data['code'];
      final timestamp = data['timestamp'];

      T? parsedData;
      if (responseData != null && fromJson != null) {
        parsedData = fromJson(responseData);
      }

      return ApiResponse<T>(
        success: success,
        message: message,
        messageAr: messageAr,
        data: parsedData,
        meta: meta,
        errors: errors,
        code: code,
        timestamp: timestamp,
      );
    }

    throw ApiException.unknown('Invalid response format');
  }

  // Handle errors
  ApiException _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return ApiException.timeout();
        
        case DioExceptionType.connectionError:
          return ApiException.network();
        
        case DioExceptionType.badResponse:
          return _handleHttpError(error);
        
        case DioExceptionType.cancel:
          return ApiException(message: 'تم إلغاء الطلب', code: 'CANCELLED');
        
        default:
          return ApiException.unknown();
      }
    }
    
    if (error is SocketException) {
      return ApiException.network();
    }
    
    return ApiException.unknown(error.toString());
  }

  // Handle HTTP errors
  ApiException _handleHttpError(DioException error) {
    final statusCode = error.response?.statusCode;
    final data = error.response?.data;

    if (data is Map<String, dynamic>) {
      final errorResponse = ErrorResponse.fromJson(data);
      return ApiException.fromErrorResponse(errorResponse, statusCode);
    }

    switch (statusCode) {
      case 401:
        return ApiException.unauthorized();
      case 403:
        return ApiException.forbidden();
      case 404:
        return ApiException.notFound();
      case 422:
        return ApiException(
          message: 'بيانات غير صحيحة',
          code: 'VALIDATION_ERROR',
          statusCode: statusCode,
        );
      case 500:
      case 502:
      case 503:
      case 504:
        return ApiException.serverError();
      default:
        return ApiException.unknown();
    }
  }

  // Get paginated response
  Future<PaginatedResponse<T>> getPaginated<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    required T Function(dynamic) fromJson,
  }) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final data = response.data;

      debugPrint('HttpService.getPaginated: Raw response data type: ${data.runtimeType}');
      if (data is Map<String, dynamic>) {
        debugPrint('HttpService.getPaginated: Response keys: ${data.keys.toList()}');
        if (data.containsKey('success')) {
          debugPrint('HttpService.getPaginated: Success: ${data['success']}');
        }
        if (data.containsKey('data')) {
          final responseData = data['data'];
          debugPrint('HttpService.getPaginated: Data type: ${responseData.runtimeType}');
          if (responseData is Map<String, dynamic>) {
            debugPrint('HttpService.getPaginated: Data keys: ${responseData.keys.toList()}');
          }
        }
      }

      if (data is Map<String, dynamic>) {
        // Vérifier si c'est une réponse API encapsulée
        if (data.containsKey('success') && data.containsKey('data')) {
          if (data['success'] == true) {
            final responseData = data['data'];

            // Cas 1: Structure avec double imbrication {success: true, data: {data: [...], current_page: 1, ...}}
            if (responseData is Map<String, dynamic> && responseData.containsKey('data') && responseData.containsKey('current_page')) {
              debugPrint('HttpService: Detected double nested structure, using inner data');
              return PaginatedResponse.fromJson(responseData, fromJson);
            }
            // Cas 2: Structure simple avec pagination au niveau racine {success: true, data: [...], current_page: 1, ...}
            else if (data.containsKey('current_page') && data.containsKey('total')) {
              debugPrint('HttpService: Detected simple structure with root-level pagination');
              // Créer une structure compatible avec PaginatedResponse
              final paginatedData = Map<String, dynamic>.from(data);
              paginatedData['data'] = responseData; // Déplacer les données vers 'data'
              paginatedData.remove('success'); // Supprimer 'success' pour éviter les conflits
              return PaginatedResponse.fromJson(paginatedData, fromJson);
            }
            // Cas 3: Données directes dans data (liste simple)
            else if (responseData is List) {
              debugPrint('HttpService: Detected simple list, creating basic pagination');
              final paginatedData = {
                'data': responseData,
                'current_page': 1,
                'last_page': 1,
                'per_page': responseData.length,
                'total': responseData.length,
                'from': responseData.isNotEmpty ? 1 : null,
                'to': responseData.length,
                'next_page_url': null,
                'prev_page_url': null,
              };
              return PaginatedResponse.fromJson(paginatedData, fromJson);
            }
            else {
              throw ApiException(message: 'Structure de réponse paginée non reconnue');
            }
          } else {
            throw ApiException(message: data['message'] ?? 'Erreur API');
          }
        } else {
          // Réponse directement paginée
          return PaginatedResponse.fromJson(data, fromJson);
        }
      }

      throw ApiException.unknown('Invalid paginated response format');
    } catch (e) {
      throw _handleError(e);
    }
  }

  // Download file
  Future<void> downloadFile(
    String path,
    String savePath, {
    Map<String, dynamic>? queryParameters,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      await _dio.download(
        path,
        savePath,
        queryParameters: queryParameters,
        onReceiveProgress: onReceiveProgress,
      );
    } catch (e) {
      throw _handleError(e);
    }
  }

  // Upload file
  Future<ApiResponse<T>> uploadFile<T>(
    String path,
    String filePath, {
    String fieldName = 'file',
    Map<String, dynamic>? data,
    T Function(dynamic)? fromJson,
    ProgressCallback? onSendProgress,
  }) async {
    try {
      final formData = FormData.fromMap({
        ...?data,
        fieldName: await MultipartFile.fromFile(filePath),
      });

      final response = await _dio.post(
        path,
        data: formData,
        onSendProgress: onSendProgress,
      );

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      throw _handleError(e);
    }
  }
}

// Auth Interceptor
class _AuthInterceptor extends Interceptor {
  final AuthService _authService;

  _AuthInterceptor(this._authService);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await _authService.getToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // Token expired or invalid, logout user
      await _authService.logout();
    }
    handler.next(err);
  }
}

// Error Interceptor
class _ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Log error for debugging
    if (kDebugMode) {
      debugPrint('API Error: ${err.message}');
      debugPrint('Status Code: ${err.response?.statusCode}');
      debugPrint('Response Data: ${err.response?.data}');
    }
    handler.next(err);
  }
}
