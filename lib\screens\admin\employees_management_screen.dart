import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:data_table_2/data_table_2.dart';
import 'dart:async';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../widgets/common/custom_error_widget.dart';
import '../../routes/app_routes.dart';

class EmployeesManagementScreen extends StatefulWidget {
  const EmployeesManagementScreen({super.key});

  @override
  State<EmployeesManagementScreen> createState() => _EmployeesManagementScreenState();
}

class _EmployeesManagementScreenState extends State<EmployeesManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedRole = 'all';
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _loadEmployees();
  }



  void _loadEmployees() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<EmployeesProvider>();
      debugPrint('EmployeesManagementScreen: Loading employees...');
      provider.loadEmployeesData().then((_) {
        debugPrint('EmployeesManagementScreen: Employees loaded successfully');
        debugPrint('EmployeesManagementScreen: Total employees: ${provider.employees.length}');
      }).catchError((error) {
        debugPrint('EmployeesManagementScreen: Error loading employees: $error');
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الموظفين'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => Navigator.pushNamed(context, AppRoutes.addEmployee),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadEmployees,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(
            child: Consumer<EmployeesProvider>(
              builder: (context, provider, child) {
                debugPrint('EmployeesManagementScreen: Building UI - State: ${provider.state}, Loading: ${provider.isLoading}, Employees count: ${provider.employees.length}');

                if (provider.isLoading && provider.employees.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('جاري تحميل الموظفين...'),
                      ],
                    ),
                  );
                }

                if (provider.state == EmployeesState.error && provider.employees.isEmpty) {
                  return CustomErrorWidget(
                    message: provider.errorMessage ?? 'حدث خطأ في تحميل البيانات',
                    onRetry: _loadEmployees,
                  );
                }

                final filteredEmployees = _getFilteredEmployees(provider.employees);

                if (filteredEmployees.isEmpty && !provider.isLoading) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.people_outline, size: 100, color: Colors.grey),
                        const SizedBox(height: 20),
                        Text(
                          provider.employees.isEmpty
                              ? 'لا توجد موظفين'
                              : 'لا توجد نتائج للبحث',
                          style: const TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                        if (provider.employees.isEmpty) ...[
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: _loadEmployees,
                            icon: const Icon(Icons.refresh),
                            label: const Text('إعادة المحاولة'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primaryBlue,
                            ),
                          ),
                        ],
                      ],
                    ),
                  );
                }

                return _buildEmployeesTable(filteredEmployees);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.pushNamed(context, AppRoutes.addEmployee),
        backgroundColor: AppColors.primaryBlue,
        child: const Icon(Icons.add, color: AppColors.textWhite),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.surfaceWhite,
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow.withValues(alpha: 0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'البحث عن موظف...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                    // Débounce la recherche pour éviter trop d'appels API
                    _debounceSearch(value);
                  },
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedRole,
                  decoration: const InputDecoration(
                    labelText: 'الدور',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الأدوار')),
                    DropdownMenuItem(value: 'admin', child: Text('مدير')),
                    DropdownMenuItem(value: 'employee', child: Text('موظف')),
                    DropdownMenuItem(value: 'supervisor', child: Text('مشرف')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedRole = value ?? 'all';
                    });
                    // Appliquer le filtre de rôle
                    if (value == 'all') {
                      context.read<EmployeesProvider>().clearFilters();
                    } else {
                      context.read<EmployeesProvider>().filterByRole(value!);
                    }
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Consumer<EmployeesProvider>(
            builder: (context, provider, child) {
              final filteredCount = _getFilteredEmployees(provider.employees).length;
              return Row(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          'إجمالي الموظفين: ${provider.employees.length}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(width: AppConstants.defaultPadding),
                        Text(
                          'النتائج المعروضة: $filteredCount',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.primaryBlue,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (provider.isLoading)
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: provider.isLoading ? null : _loadEmployees,
                    icon: const Icon(Icons.refresh),
                    tooltip: 'تحديث القائمة',
                    iconSize: 20,
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeesTable(List<User> employees) {
    return Container(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.surfaceWhite,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow.withValues(alpha: 0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DataTable2(
        columnSpacing: 12,
        horizontalMargin: 12,
        minWidth: 600,
        columns: const [
          DataColumn2(
            label: Text('الاسم', style: TextStyle(fontWeight: FontWeight.bold)),
            size: ColumnSize.L,
          ),
          DataColumn2(
            label: Text('البريد الإلكتروني', style: TextStyle(fontWeight: FontWeight.bold)),
            size: ColumnSize.L,
          ),
          DataColumn2(
            label: Text('الدور', style: TextStyle(fontWeight: FontWeight.bold)),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('الحالة', style: TextStyle(fontWeight: FontWeight.bold)),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('الإجراءات', style: TextStyle(fontWeight: FontWeight.bold)),
            size: ColumnSize.M,
          ),
        ],
        rows: employees.map((employee) => _buildEmployeeRow(employee)).toList(),
      ),
    );
  }

  DataRow _buildEmployeeRow(User employee) {
    return DataRow(
      cells: [
        DataCell(
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: AppColors.primaryBlueLight,
                child: Text(
                  employee.name.isNotEmpty ? employee.name[0].toUpperCase() : 'U',
                  style: const TextStyle(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      employee.name,
                      style: const TextStyle(fontWeight: FontWeight.w600),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      'ID: ${employee.id}',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        DataCell(
          Text(
            employee.email,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getRoleColor(employee.role).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: _getRoleColor(employee.role).withValues(alpha: 0.3)),
            ),
            child: Text(
              _getRoleText(employee.role),
              style: TextStyle(
                color: _getRoleColor(employee.role),
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
            ),
            child: const Text(
              'نشط',
              style: TextStyle(
                color: AppColors.success,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.visibility, size: 20),
                onPressed: () => Navigator.pushNamed(
                  context,
                  AppRoutes.employeeDetails,
                  arguments: {'employeeId': employee.id},
                ),
                tooltip: 'عرض التفاصيل',
              ),
              IconButton(
                icon: const Icon(Icons.edit, size: 20),
                onPressed: () => Navigator.pushNamed(
                  context,
                  AppRoutes.editEmployee,
                  arguments: {'employee': employee},
                ),
                tooltip: 'تعديل',
              ),
              IconButton(
                icon: const Icon(Icons.delete, size: 20, color: AppColors.error),
                onPressed: () => _showDeleteDialog(employee),
                tooltip: 'حذف',
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<User> _getFilteredEmployees(List<User> employees) {
    return employees.where((employee) {
      final matchesSearch = _searchQuery.isEmpty ||
          employee.name.toLowerCase().contains(_searchQuery) ||
          employee.email.toLowerCase().contains(_searchQuery);
      
      final matchesRole = _selectedRole == 'all' || employee.role == _selectedRole;
      
      return matchesSearch && matchesRole;
    }).toList();
  }

  Color _getRoleColor(String role) {
    switch (role) {
      case 'admin':
        return AppColors.error;
      case 'supervisor':
        return AppColors.warning;
      case 'employee':
      default:
        return AppColors.info;
    }
  }

  String _getRoleText(String role) {
    switch (role) {
      case 'admin':
        return 'مدير';
      case 'supervisor':
        return 'مشرف';
      case 'employee':
      default:
        return 'موظف';
    }
  }

  void _showDeleteDialog(User employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الموظف "${employee.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteEmployee(employee);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _deleteEmployee(User employee) async {
    try {
      await context.read<EmployeesProvider>().deleteEmployee(employee.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف الموظف بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حذف الموظف: $e')),
        );
      }
    }
  }

  void _debounceSearch(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query.trim().isEmpty) {
        context.read<EmployeesProvider>().clearFilters();
      } else {
        context.read<EmployeesProvider>().searchEmployees(query.trim());
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }
}
