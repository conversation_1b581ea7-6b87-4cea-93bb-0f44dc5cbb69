import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - Modern Blue Theme (Material Design 3)
  static const Color primaryBlue = Color(0xFF1976D2);
  static const Color primaryBlueDark = Color(0xFF0D47A1);
  static const Color primaryBlueLight = Color(0xFF42A5F5);
  static const Color primaryBlueAccent = Color(0xFF2196F3);

  // Secondary Colors
  static const Color secondaryBlue = Color(0xFF1565C0);
  static const Color secondaryBlueLight = Color(0xFF64B5F6);
  static const Color secondaryBlueDark = Color(0xFF0277BD);

  // Background Colors - Modern neutral palette
  static const Color backgroundWhite = Color(0xFFFFFFFF);
  static const Color backgroundGrey = Color(0xFFF8F9FA);
  static const Color backgroundLight = Color(0xFFFCFCFC);
  static const Color backgroundDark = Color(0xFF121212);

  // Surface Colors - Enhanced for Material Design 3
  static const Color surfaceWhite = Color(0xFFFFFFFF);
  static const Color surfaceGrey = Color(0xFFF5F7FA);
  static const Color surfaceBlue = Color(0xFFE8F4FD);
  static const Color surfaceDark = Color(0xFF1E1E1E);

  // Text Colors - Improved contrast
  static const Color textPrimary = Color(0xFF1A1A1A);
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textHint = Color(0xFF9CA3AF);
  static const Color textWhite = Color(0xFFFFFFFF);
  static const Color textBlue = Color(0xFF1976D2);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFF81C784);
  static const Color successDark = Color(0xFF388E3C);

  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFB74D);
  static const Color warningDark = Color(0xFFF57C00);

  static const Color error = Color(0xFFF44336);
  static const Color errorLight = Color(0xFFE57373);
  static const Color errorDark = Color(0xFFD32F2F);

  static const Color info = Color(0xFF2196F3);
  static const Color infoLight = Color(0xFF64B5F6);
  static const Color infoDark = Color(0xFF1976D2);

  // Border Colors
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderMedium = Color(0xFFBDBDBD);
  static const Color borderDark = Color(0xFF757575);
  static const Color borderBlue = Color(0xFF2196F3);

  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryBlue, primaryBlueDark],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondaryBlue, secondaryBlueDark],
  );

  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [success, successDark],
  );

  static const LinearGradient warningGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [warning, warningDark],
  );

  static const LinearGradient errorGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [error, errorDark],
  );

  // Card Colors
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color cardShadow = Color(0x0F000000);
  static const Color cardBorder = Color(0xFFE0E0E0);

  // Button Colors
  static const Color buttonPrimary = primaryBlue;
  static const Color buttonSecondary = Color(0xFF6C757D);
  static const Color buttonSuccess = success;
  static const Color buttonWarning = warning;
  static const Color buttonError = error;
  static const Color buttonDisabled = Color(0xFFE0E0E0);

  // Input Colors
  static const Color inputBackground = Color(0xFFF8F9FA);
  static const Color inputBorder = Color(0xFFDEE2E6);
  static const Color inputFocused = primaryBlue;
  static const Color inputError = error;
  static const Color inputSuccess = success;

  // Navigation Colors
  static const Color navigationBackground = backgroundWhite;
  static const Color navigationSelected = primaryBlue;
  static const Color navigationUnselected = Color(0xFF9E9E9E);

  // Attendance Status Colors
  static const Color attendancePresent = success;
  static const Color attendanceAbsent = error;
  static const Color attendanceLate = warning;
  static const Color attendanceEarly = info;

  // Location Status Colors
  static const Color locationInRange = success;
  static const Color locationOutOfRange = error;
  static const Color locationUnknown = warning;

  // Role Colors
  static const Color adminRole = Color(0xFF9C27B0);
  static const Color employeeRole = Color(0xFF607D8B);

  // Chart Colors
  static const List<Color> chartColors = [
    primaryBlue,
    success,
    warning,
    error,
    info,
    Color(0xFF9C27B0),
    Color(0xFF607D8B),
    Color(0xFF795548),
  ];

  // Opacity Values
  static const double opacityDisabled = 0.38;
  static const double opacityMedium = 0.54;
  static const double opacityHigh = 0.87;
  static const double opacityFull = 1.0;

  // Material Color Swatches
  static const MaterialColor primarySwatch = MaterialColor(
    0xFF2196F3,
    <int, Color>{
      50: Color(0xFFE3F2FD),
      100: Color(0xFFBBDEFB),
      200: Color(0xFF90CAF9),
      300: Color(0xFF64B5F6),
      400: Color(0xFF42A5F5),
      500: Color(0xFF2196F3),
      600: Color(0xFF1E88E5),
      700: Color(0xFF1976D2),
      800: Color(0xFF1565C0),
      900: Color(0xFF0D47A1),
    },
  );

  // Helper Methods
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }

  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }

  // Theme-specific color getters
  static Color getTextColor(bool isDark) {
    return isDark ? textWhite : textPrimary;
  }

  static Color getBackgroundColor(bool isDark) {
    return isDark ? backgroundDark : backgroundWhite;
  }

  static Color getSurfaceColor(bool isDark) {
    return isDark ? surfaceDark : surfaceWhite;
  }

  static Color getBorderColor(bool isDark) {
    return isDark ? borderDark : borderLight;
  }
}
